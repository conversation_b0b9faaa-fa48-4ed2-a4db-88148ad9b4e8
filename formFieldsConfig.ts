/**
 * 表单字段验证配置
 *
 * 配置说明:
 * - required: 是否为必填项
 * - label: 字段的显示名称，用于错误提示
 * - prop: 表单属性名称，用于表单验证
 * - maxLength: 最大长度限制（可选）
 * - validationType: 验证类型，用于选择合适的验证方法（可选）
 */

interface FieldConfig {
  required: boolean
  label: string
  prop?: string
  maxLength?: number
  validationType?: 'general' | 'oversea' | 'vat' | 'invoice' | 'address' | 'phone' | 'email' | 'code'
}

// 字段配置对象类型
interface FieldsConfig {
  [key: string]: FieldConfig
}

// 电子发票字段配置
export const electroInvoiceFields: FieldsConfig = {
  'electroInvoice.personalName': {
    required: false,
    label: '个人名称',
    prop: 'electroInvoice:personalName',
    maxLength: 25,
    validationType: 'general'
  },
  'electroInvoice.electroCompanyName': {
    required: true,
    label: '单位名称',
    prop: 'electroInvoice:electroCompanyName',
    maxLength: 100,
    validationType: 'vat'
  },
  'electroInvoice.invoiceCode': {
    required: true,
    label: '纳税人识别号',
    prop: 'electroInvoice:invoiceCode',
    maxLength: 20,
    validationType: 'code'
  },
  'electroInvoice.phone': {
    required: true,
    label: '收票人手机',
    prop: 'electroInvoice:phone',
    maxLength: 11,
    validationType: 'phone'
  },
  'electroInvoice.email': {
    required: false,
    label: '收票人邮箱',
    prop: 'electroInvoice:email',
    maxLength: 50,
    validationType: 'email'
  },
  'electroInvoice.regTel': {
    required: false,
    label: '单位电话',
    prop: 'electroInvoice:regTel',
    maxLength: 20,
    validationType: 'phone'
  },
  'electroInvoice.regBank': {
    required: false,
    label: '开户银行',
    prop: 'electroInvoice:regBank',
    maxLength: 49,
    validationType: 'general'
  },
  'electroInvoice.regAccount': {
    required: false,
    label: '银行账号',
    prop: 'electroInvoice:regAccount',
    maxLength: 99,
    validationType: 'general'
  },
  'electroInvoice.regAddress': {
    required: false,
    label: '单位地址',
    prop: 'electroInvoice:regAddress',
    maxLength: 124,
    validationType: 'address'
  },
}

// 专用发票字段配置
export const vatInvoiceFields: FieldsConfig = {
  'vat.companyName': {
    required: true,
    label: '单位名称',
    prop: 'vat:companyName',
    maxLength: 100,
    validationType: 'vat'
  },
  'vat.code': {
    required: true,
    label: '纳税人识别号',
    prop: 'vat:code',
    maxLength: 20,
    validationType: 'code'
  },
  'vat.regAddr': {
    required: true,
    label: '注册地址',
    prop: 'vat:regAddr',
    maxLength: 124,
    validationType: 'vat'
  },
  'vat.regPhone': {
    required: true,
    label: '注册电话',
    prop: 'vat:regPhone',
    maxLength: 50,
    validationType: 'vat'
  },
  'vat.regBank': {
    required: true,
    label: '开户银行',
    prop: 'vat:regBank',
    maxLength: 49,
    validationType: 'vat'
  },
  'vat.regBankAccount': {
    required: true,
    label: '银行账户',
    prop: 'vat:regBankAccount',
    maxLength: 50,
    validationType: 'vat'
  },
  'vat.email': {
    required: false,
    label: '收票人邮箱',
    prop: 'vat:email',
    maxLength: 50,
    validationType: 'email'
  },
  'vat.invoiceConsigneeEditVO.consigneeName': {
    required: true,
    label: '收票人姓名',
    prop: 'vat:invoiceConsigneeEditVO:consigneeName',
    maxLength: 25,
    validationType: 'general'
  },
  'vat.invoiceConsigneeEditVO.phone': {
    required: true,
    label: '收票人手机',
    prop: 'vat:invoiceConsigneeEditVO:phone',
    maxLength: 11,
    validationType: 'phone'
  },
  'vat.invoiceConsigneeEditVO.address': {
    required: true,
    label: '收票人地址',
    prop: 'vat:invoiceConsigneeEditVO:address',
    maxLength: 50,
    validationType: 'address'
  },
}

// 电子专用发票字段配置
const eleVatInvoiceFields: FieldsConfig = {
  'eleVat.companyName': {
    required: true,
    label: '单位名称',
    prop: 'eleVat:companyName',
    maxLength: 100,
    validationType: 'vat'
  },
  'eleVat.code': {
    required: true,
    label: '纳税人识别号',
    prop: 'eleVat:code',
    maxLength: 20,
    validationType: 'code'
  },
  'eleVat.regAddr': {
    required: false,
    label: '注册地址',
    prop: 'eleVat:regAddr',
    maxLength: 124,
    validationType: 'vat'
  },
  'eleVat.regPhone': {
    required: false,
    label: '注册电话',
    prop: 'eleVat:regPhone',
    maxLength: 50,
    validationType: 'vat'
  },
  'eleVat.regBank': {
    required: false,
    label: '开户银行',
    prop: 'eleVat:regBank',
    maxLength: 49,
    validationType: 'vat'
  },
  'eleVat.regBankAccount': {
    required: false,
    label: '银行账户',
    prop: 'eleVat:regBankAccount',
    maxLength: 50,
    validationType: 'vat'
  },
  'eleVat.email': {
    required: false,
    label: '收票人邮箱',
    prop: 'eleVat:email',
    maxLength: 50,
    validationType: 'email'
  },
}

// 普通发票字段配置
export const normalInvoiceFields: FieldsConfig = {
  'normalInvoice.companyName': {
    required: true,
    label: '单位名称',
    prop: 'normalInvoice:companyName',
    maxLength: 100,
    validationType: 'invoice'
  },
  'normalInvoice.personalName': {
    required: false,
    label: '个人名称',
    prop: 'normalInvoice:personalName',
    maxLength: 25,
    validationType: 'general'
  },
  'normalInvoice.invoiceCode': {
    required: true,
    label: '纳税人识别号',
    prop: 'normalInvoice:invoiceCode',
    maxLength: 20,
    validationType: 'code'
  },
  'normalInvoice.regTel': {
    required: false,
    label: '单位电话',
    prop: 'normalInvoice:regTel',
    maxLength: 20,
    validationType: 'phone'
  },
  'normalInvoice.regBank': {
    required: false,
    label: '开户银行',
    prop: 'normalInvoice:regBank',
    maxLength: 49,
    validationType: 'invoice'
  },
  'normalInvoice.regAccount': {
    required: false,
    label: '银行账户',
    prop: 'normalInvoice:regAccount',
    maxLength: 99,
    validationType: 'invoice'
  },
  'normalInvoice.regAddress': {
    required: false,
    label: '单位地址',
    prop: 'normalInvoice:regAddress',
    maxLength: 124,
    validationType: 'address'
  },
}

// 合并所有配置
export const formFieldsConfig: FieldsConfig = {
  ...electroInvoiceFields,
  ...vatInvoiceFields,
  ...eleVatInvoiceFields,
  ...normalInvoiceFields,
}

/**
 * 获取字段配置
 * @param field 字段名称
 * @returns 字段配置对象，默认为必填字段
 */
export const getFieldConfig = (field: string): FieldConfig => {
  // 处理可能的嵌套路径
  if (formFieldsConfig[field]) {
    return formFieldsConfig[field]
  }

  // 尝试匹配部分字段名（如果传入的是完整路径而配置中只有部分路径）
  const fieldName = field.includes('.') ? field.split('.').pop() || '' : field

  // 遍历配置查找匹配的字段
  for (const key in formFieldsConfig) {
    if (key.endsWith(fieldName)) {
      return formFieldsConfig[key]
    }
  }

  // 默认配置：必填，使用字段名作为标签
  return { required: true, label: fieldName }
}

/**
 * 根据验证类型获取推荐的最大长度
 * @param validationType 验证类型
 * @returns 推荐的最大长度
 */
export const getRecommendedMaxLength = (validationType?: string): number => {
  switch (validationType) {
    case 'phone':
      return 20
    case 'email':
      return 50
    case 'code':
      return 20
    case 'address':
      return 50
    case 'vat':
      return 100
    case 'invoice':
      return 100
    case 'oversea':
      return 100
    case 'general':
    default:
      return 100
  }
}

/**
 * 验证字段配置的完整性
 * @param config 字段配置
 * @returns 验证结果和建议
 */
export const validateFieldConfig = (config: FieldConfig): { isValid: boolean; suggestions: string[] } => {
  const suggestions: string[] = []
  let isValid = true

  // 检查必填字段
  if (!config.label) {
    suggestions.push('建议设置字段标签(label)')
    isValid = false
  }

  // 检查最大长度设置
  if (config.validationType && !config.maxLength) {
    const recommendedLength = getRecommendedMaxLength(config.validationType)
    suggestions.push(`建议为${config.validationType}类型字段设置最大长度: ${recommendedLength}`)
  }

  // 检查验证类型与字段名的匹配度
  if (config.validationType) {
    const fieldName = config.label.toLowerCase()
    const type = config.validationType

    if (type === 'phone' && !fieldName.includes('电话') && !fieldName.includes('手机')) {
      suggestions.push('字段名称与验证类型(phone)可能不匹配')
    }

    if (type === 'email' && !fieldName.includes('邮箱') && !fieldName.includes('邮件')) {
      suggestions.push('字段名称与验证类型(email)可能不匹配')
    }

    if (type === 'address' && !fieldName.includes('地址')) {
      suggestions.push('字段名称与验证类型(address)可能不匹配')
    }
  }

  return { isValid, suggestions }
}
