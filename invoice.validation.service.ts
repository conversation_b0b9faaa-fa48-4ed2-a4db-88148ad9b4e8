// 导入验证常量
import {
  // 发票相关常量
  INVOICE_ACCOUNT_FORMAT_REGEX,
  INVOICE_ADDRESS_CHINESE_REGEX,
  INVOICE_ADDRESS_GENERAL_SPECIAL_CHARS_REGEX,
  INVOICE_EMAIL_FORMAT_REGEX,
  INVOICE_PHONE_FORMAT_REGEX,
  INVOICE_TEL_FORMAT_REGEX,

  // 增值税发票相关常量
  VAT_ADDRESS_SPECIAL_CHARS_REGEX,
  VAT_BANK_ACCOUNT_FORMAT_REGEX,
  VAT_CODE_FORMAT_REGEX,
  VAT_CODE_SPECIAL_CHARS_REGEX,
  VAT_COMPANY_NAME_SPECIAL_CHARS_REGEX,
  VAT_CONSIGNEE_NAME_SPECIAL_CHARS_REGEX,
  VAT_PHONE_LENGTH_REGEX,
  VAT_PHONE_SPECIAL_CHARS_REGEX,

  // 通用非法字符验证常量
  GENERAL_FORBIDDEN_CHARS_REGEX,
  EMOJI_AND_SPECIAL_CHARS_REGEX,
} from "./validation-constants.ts";

// 字符替换常量，用于非法字符检查
const FORBIDDEN_CHARS = {
  // 标准非法字符列表
  STANDARD: ["*", "'", "\\", "$", "^", ";", "<", ">", '"', "=", "{", "}"],
  // 海外业务非法字符列表
  OVERSEAS: [
    "*",
    "<",
    ">",
    "$",
    "{",
    "}",
    "=",
    '"',
    "^",
    "'",
    "\\",
    "`",
    "!",
    "[",
    "]",
    ":",
    "?",
    '"',
    "'",
  ],
  // 备注字段非法字符列表
  REMARK: [
    "*",
    "--",
    "/",
    "+",
    "'",
    "\\",
    "$",
    "^",
    ";",
    "<",
    ">",
    '"',
    "=",
    "{",
    "}",
  ],
};

// 字符替换函数
function replaceCharsWithAt(str: string, chars: string[]): string {
  let result = str;
  for (const char of chars) {
    result = result.replace(char, "@");
  }
  return result;
}

// Common validation result type
interface ValidationResult {
  isValid: boolean;
  message: string;
}

export const invoiceValidationService = {
  /**
   * @description 验证纳税人识别号是否合法
   * @param {string} code - 纳税人识别号
   * @returns {boolean} 是否合法
   */
  validateTaxCode(code: string): boolean {
    if (!code) return true; // 为空可以通过，有些情况下是非必填
    // 15-20位数字或大写字母
    return VAT_CODE_FORMAT_REGEX.test(code);
  },

  /**
   * @description 检查字段是否为空
   * @param {any} value - 需要检查的值
   * @returns {boolean} 是否为空
   */
  isEmpty(value: any): boolean {
    if (
      value == null ||
      value === "" ||
      value === "undefined" ||
      value === undefined ||
      value === "null"
    ) {
      return true;
    } else if (typeof value === "string") {
      value = value.replace(/\s+/g, ""); // 替换空白字符
      if (value === "") {
        return true;
      }
    }
    return false;
  },

  isForbidden(value: string): boolean {
    return VAT_CODE_SPECIAL_CHARS_REGEX.test(value);
  },

  /**
   * @description 检查邮箱格式
   * @param {string} email - 电子邮箱
   * @returns {boolean} 是否合法
   */
  checkEmail(email: string, isRequired: boolean = false): boolean {
    if (!email) return !isRequired; // 为空时通过验证，由调用者决定是否必填
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email); // 简单邮箱验证
  },

  /**
   * @description 去掉字符串头尾空格
   * @param {string} str - 字符串
   * @returns {string} 处理后的字符串
   */
  trim(str: string): string {
    if (!str) return "";
    return str.replace(/^\s+|\s+$/g, ""); // 去除头尾空格
  },
  /**
   * @description 检查是否含有非法字符
   * @param {string} tempStr - 要检查的字符串
   * @returns {boolean} true表示通过验证（无非法字符），false表示含有非法字符
   */
  isForbiddenChars(tempStr: string): boolean {
    if (!tempStr) return true;
    const trimmedStr = this.trim(tempStr);
    return !GENERAL_FORBIDDEN_CHARS_REGEX.test(trimmedStr);
  },

  /**
   * @description 检查是否含有表情符号或特殊Unicode字符
   * @param {string} tempStr - 要检查的字符串
   * @returns {boolean} true表示含有表情符号，false表示不含有
   */
  hasEmoji(tempStr: string): boolean {
    if (!tempStr) return false;
    return EMOJI_AND_SPECIAL_CHARS_REGEX.test(tempStr);
  },

  /**
   * @description 验证通用文本字段（使用通用非法字符检查）
   * @param {string} text - 要验证的文本
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateGeneralText(
    text: string,
    isRequired: boolean = true,
    label: string = "文本"
  ): ValidationResult {
    return this.baseValidate(
      text,
      isRequired,
      [
        (value: string) =>
          !this.isForbiddenChars(value) ? `${label}含有非法字符` : null,
        (value: string) =>
          this.hasEmoji(value) ? `${label}不可含特殊字符或表情符号` : null,
      ],
      label
    );
  },

  /**
   * @description 验证单位发票抬头名称
   * @param {string} companyName - 单位名称
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateVatCompanyName(
    companyName: string,
    isRequired: boolean = true,
    label: string = "单位名称"
  ): ValidationResult {
    return this.baseValidate(
      companyName,
      isRequired,
      [
        this.validateLength(2, 100, `${label}长度需在2-100个字符之间`),
        this.validateNoSpecialChars(`${label}含有非法字符！`),
      ],
      label
    );
  },

  /**
   * @description 验证纳税人识别号
   * @param {string} code - 纳税人识别号
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateVatCode(
    code: string,
    isRequired: boolean = true,
    label: string = "纳税人识别号"
  ): ValidationResult {
    return this.baseValidate(
      code,
      isRequired,
      [
        (value: string) =>
          !VAT_CODE_FORMAT_REGEX.test(value) ? `${label}错误，请检查！` : null,
        this.validateNoIllegalChars(`${label}含有非法字符！`),
      ],
      label
    );
  },

  /**
   * @description 验证发票邮箱
   * @param {string} email - 邮箱
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateInvoiceEmail(
    email: string,
    isRequired: boolean = false,
    label: string = "邮箱"
  ): ValidationResult {
    return this.baseValidate(
      email,
      isRequired,
      [
        this.validateLength(0, 50, `${label}长度不能大于50位`),
        this.validateNoEmoji(`${label}不可含特殊字符`),
        (value: string) =>
          !INVOICE_EMAIL_FORMAT_REGEX.test(value) && !this.checkEmail(value)
            ? `${label}格式不正确`
            : null,
      ],
      label
    );
  },

  /**
   * @description 验证手机号码
   * @param {string} mobile - 手机号
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateMobile(
    mobile: string,
    isRequired: boolean = true,
    label: string = "手机号码"
  ): ValidationResult {
    return this.baseValidate(
      mobile,
      isRequired,
      [this.validatePattern(INVOICE_PHONE_FORMAT_REGEX, `${label}格式不正确`)],
      label
    );
  },

  /**
   * @description 验证注册电话
   * @param {string} tel - 注册电话
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateRegTel(
    tel: string,
    isRequired: boolean = false,
    label: string = "注册电话"
  ): ValidationResult {
    return this.baseValidate(
      tel,
      isRequired,
      [
        this.validateLength(0, 20, `${label}长度需小于20位`),
        this.validatePattern(
          INVOICE_TEL_FORMAT_REGEX,
          `${label}不可含特殊字符`
        ),
        this.validateNoEmoji(`${label}不可含特殊字符`),
      ],
      label
    );
  },

  /**
   * @description 验证银行名称
   * @param {string} bank - 银行名称
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateRegBank(
    bank: string,
    isRequired: boolean = false,
    label: string = "开户银行"
  ): ValidationResult {
    return this.baseValidate(
      bank,
      isRequired,
      [
        this.validateLength(0, 49, `${label}长度需小于50位`),
        this.validateNoIllegalChars(`${label}不可含特殊字符或无汉字`),
        this.validateHasChinese(`${label}不可含特殊字符或无汉字`),
        this.validateNoEmoji(`${label}不可含特殊字符`),
      ],
      label
    );
  },

  /**
   * @description 验证银行账号
   * @param {string} account - 银行账号
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateRegAccount(
    account: string,
    isRequired: boolean = false,
    label: string = "银行账号"
  ): ValidationResult {
    return this.baseValidate(
      account,
      isRequired,
      [
        (value: string) =>
          value.length < 6 || value.length > 99
            ? `${label}需大于5位且小于100位`
            : null,
        this.validatePattern(
          INVOICE_ACCOUNT_FORMAT_REGEX,
          `${label}只可填写数字`
        ),
        this.validateNoEmoji(`${label}不可含特殊字符`),
      ],
      label
    );
  },

  /**
   * @description 验证地址
   * @param {string} address - 地址
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateRegAddress(
    address: string,
    isRequired: boolean = false,
    label: string = "注册地址"
  ): ValidationResult {
    return this.baseValidate(
      address,
      isRequired,
      [
        this.validateLength(0, 124, `${label}长度需小于125位`),
        this.validateNoIllegalChars(`${label}不可含特殊字符或无汉字`),
        this.validateHasChinese(`${label}不可含特殊字符或无汉字`),
        this.validateNoEmoji(`${label}不可含特殊字符`),
      ],
      label
    );
  },

  /**
   * @description 验证增值税发票注册地址
   * @param {string} address - 注册地址
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateVatAddress(
    address: string,
    isRequired: boolean = false,
    label: string = "注册地址"
  ): ValidationResult {
    return this.baseValidate(
      address,
      isRequired,
      [
        (value: string) =>
          VAT_ADDRESS_SPECIAL_CHARS_REGEX.test(value)
            ? `${label}含有非法字符！`
            : null,
      ],
      label
    );
  },

  /**
   * @description 验证增值税发票银行账户
   * @param {string} account - 银行账户
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateVatBankAccount(
    account: string,
    isRequired: boolean = false,
    label: string = "银行账户"
  ): ValidationResult {
    return this.baseValidate(
      account,
      isRequired,
      [
        this.validatePattern(
          VAT_BANK_ACCOUNT_FORMAT_REGEX,
          `${label}含有非法字符！`
        ),
      ],
      label
    );
  },

  /**
   * @description 验证增值税发票注册电话
   * @param {string} phone - 注册电话
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateVatPhone(
    phone: string,
    isRequired: boolean = false,
    label: string = "注册电话"
  ): ValidationResult {
    return this.baseValidate(
      phone,
      isRequired,
      [
        (value: string) =>
          !VAT_PHONE_LENGTH_REGEX.test(value)
            ? `${label}过长，请重新输入！`
            : null,
        (value: string) =>
          VAT_PHONE_SPECIAL_CHARS_REGEX.test(value)
            ? `${label}含有非法字符，请重新输入！`
            : null,
      ],
      label
    );
  },

  /**
   * @description 验证增值税发票收票人姓名
   * @param {string} name - 收票人姓名
   * @param {boolean} isRequired - 是否必填
   * @param {string} label - 字段标签
   * @returns {ValidationResult} 验证结果
   */
  validateVatConsigneeName(
    name: string,
    isRequired: boolean = true,
    label: string = "收票人姓名"
  ): ValidationResult {
    return this.baseValidate(
      name,
      isRequired,
      [
        (value: string) =>
          VAT_CONSIGNEE_NAME_SPECIAL_CHARS_REGEX.test(value)
            ? `${label}中含有非法字符`
            : null,
      ],
      label
    );
  },

  /**
   * 基础验证函数 - 通用验证逻辑
   * @param value 要验证的值
   * @param isRequired 是否必填
   * @param validators 验证函数数组，每个函数返回错误消息或空字符串
   * @param label 字段标签
   * @returns 验证结果
   */
  baseValidate(
    value: string,
    isRequired: boolean = true,
    validators: Array<(val: string) => string | null>,
    label: string = ""
  ): ValidationResult {
    // 处理非必填且为空的情况
    if (!isRequired && (!value || value.toString().trim() === "")) {
      return { isValid: true, message: "" };
    }

    // 过滤非法字符
    value = replaceCharsWithAt(value, FORBIDDEN_CHARS.STANDARD);

    // 处理必填但为空的情况
    if (isRequired && (!value || value.toString().trim() === "")) {
      return {
        isValid: false,
        message: label ? `${label}不能为空！` : "该字段不能为空！",
      };
    }

    // 依次执行所有验证函数
    for (const validator of validators) {
      const errorMessage = validator(value);
      if (errorMessage) {
        return { isValid: false, message: errorMessage };
      }
    }

    return { isValid: true, message: "" };
  },

  // 通用验证检查函数
  /**
   * @description 验证长度
   * @param min - 最小长度
   * @param max - 最大长度
   * @param errorMessage - 错误信息
   * @returns 验证结果
   */
  validateLength(min: number, max: number, errorMessage: string) {
    return (value: string): string | null => {
      if (value.length < min || value.length > max) {
        return errorMessage;
      }
      return null;
    };
  },

  /**
   * @description 验证是否符合正则表达式
   * @param pattern - 正则表达式
   * @param errorMessage - 错误信息
   * @returns 验证结果
   */
  validatePattern(pattern: RegExp, errorMessage: string) {
    return (value: string): string | null => {
      if (!pattern.test(value)) {
        return errorMessage;
      }
      return null;
    };
  },

  /**
   * @description 验证是否含有特殊字符
   * @param errorMessage - 错误信息
   * @returns 验证结果
   */
  validateNoSpecialChars(errorMessage: string) {
    return (value: string): string | null => {
      if (VAT_COMPANY_NAME_SPECIAL_CHARS_REGEX.test(value)) {
        return errorMessage;
      }
      return null;
    };
  },
  /**
   * @description 验证是否含有非法字符
   * @param errorMessage - 错误信息
   * @returns 验证结果
   */
  validateNoIllegalChars(errorMessage: string) {
    return (value: string): string | null => {
      if (VAT_CODE_SPECIAL_CHARS_REGEX.test(value)) {
        return errorMessage;
      }
      return null;
    };
  },

  /**
   * @description 验证是否含有中文
   * @param errorMessage - 错误信息
   * @returns 验证结果
   */
  validateHasChinese(errorMessage: string) {
    return (value: string): string | null => {
      if (!INVOICE_ADDRESS_CHINESE_REGEX.test(value)) {
        return errorMessage;
      }
      return null;
    };
  },

  /**
   * @description 验证是否含有emoji
   * @param errorMessage - 错误信息
   * @returns 验证结果
   */
  validateNoEmoji(errorMessage: string) {
    return (value: string): string | null => {
      if (INVOICE_ADDRESS_GENERAL_SPECIAL_CHARS_REGEX.test(value)) {
        return errorMessage;
      }
      return null;
    };
  },
};
