// ==================== 正则表达式常量 ====================

/**
 * 发票银行账户格式验证 - invoiceRegAccount
 * 用途：验证银行账号只能是数字
 * 错误消息：银行账号只可填写数字
 */
export const INVOICE_ACCOUNT_FORMAT_REGEX = /^[0-9]*$/

/**
 * 发票注册地址汉字验证 - invoiceRegAddress
 * 用途：验证注册地址包含汉字
 * 错误消息：注册地址不可含特殊字符或无汉字
 */
export const INVOICE_ADDRESS_CHINESE_REGEX = /[\u4E00-\u9FFF]+/

/**
 * 发票注册地址通用特殊字符验证 - invoiceRegAddress
 * 用途：验证注册地址不含特殊字符
 * 错误消息：注册地址不可含特殊字符
 */
export const INVOICE_ADDRESS_GENERAL_SPECIAL_CHARS_REGEX =
  /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac]/

/**
 * 发票邮箱格式验证 - invoiceRegSprEmail
 * 用途：验证邮箱格式
 * 错误消息：邮箱格式不正确
 */
export const INVOICE_EMAIL_FORMAT_REGEX =
  /^[0-9a-zA-Z_\-.]{1}\**@\w+([-.]w+)*\.\w+([-.]w+)*(\s*$)|(^\s*)\w+([-+.']\w+)*@\w+([-.]w+)*\.\w+([-.]w+)*(\s*$)/

/**
 * 发票手机号格式验证 - invoiceRegSprPhone
 * 用途：验证手机号格式
 * 错误消息：手机号码格式不正确
 */
export const INVOICE_PHONE_FORMAT_REGEX = /^\d{3}\*\*\*\*\d{4}$|^\d{11}$/

/**
 * 发票注册电话格式验证 - invoiceRegTel
 * 用途：验证注册电话只能包含数字和横线
 * 错误消息：注册电话不可含特殊字符
 */
export const INVOICE_TEL_FORMAT_REGEX = /^[0-9-]*$/

// ==================== 增值税发票相关正则表达式 ====================

/**
 * 增值税发票注册地址特殊字符验证 - invoiceRegVatAddress
 * 用途：验证注册地址不含特殊字符
 * 错误消息：注册地址含有非法字符！
 */
export const VAT_ADDRESS_SPECIAL_CHARS_REGEX = /[><&]+/

/**
 * 增值税发票银行账户格式验证 - invoiceRegVatBankAccount
 * 用途：验证银行账户只能包含数字、横线和空格
 * 错误消息：银行帐户含有非法字符！
 */
export const VAT_BANK_ACCOUNT_FORMAT_REGEX = /^[0-9\-\s]*$/

/**
 * 增值税发票纳税人识别号格式验证 - invoiceRegVatCode
 * 用途：验证纳税人识别号格式（15-20位字母数字）
 * 错误消息：纳税人识别号错误，请检查！
 */
export const VAT_CODE_FORMAT_REGEX = /^([a-zA-Z0-9]){15,20}$/

/**
 * 增值税发票纳税人识别号特殊字符验证 - invoiceRegVatCode
 * 用途：验证纳税人识别号不含特殊字符
 * 错误消息：纳税人识别号含有非法字符！
 */
export const VAT_CODE_SPECIAL_CHARS_REGEX = /[`~!@$%^&*+=|':;',\\\\\\\\<>/?~！￥……&*&;|'；：""'，？\\"]+/

/**
 * 增值税发票单位名称特殊字符验证 - invoiceRegVatCompanyName
 * 用途：验证单位名称不含特殊字符
 * 错误消息：单位名称含有非法字符！
 */
export const VAT_COMPANY_NAME_SPECIAL_CHARS_REGEX = /[><&]+/

/**
 * 增值税发票收票人姓名特殊字符验证 - invoiceRegVatConsigneeName
 * 用途：验证收票人姓名不含特殊字符
 * 错误消息：收票人姓名中含有非法字符
 */
export const VAT_CONSIGNEE_NAME_SPECIAL_CHARS_REGEX = /[`~!@$%^&*+=|':;',\\\\\\\\<>/?~！￥……&*&;|'；：""'，？\\"]+/

/**
 * 增值税发票注册电话长度验证 - invoiceRegVatPhone
 * 用途：验证注册电话长度不超过50位
 * 错误消息：注册电话过长，请重新输入！
 */
export const VAT_PHONE_LENGTH_REGEX = /^.{1,50}$/

/**
 * 增值税发票注册电话特殊字符验证 - invoiceRegVatPhone
 * 用途：验证注册电话不含特殊字符
 * 错误消息：注册电话含有非法字符，请重新输入！
 */
export const VAT_PHONE_SPECIAL_CHARS_REGEX = /[><&]+/

// ==================== 通用非法字符验证正则表达式 ====================

/**
 * 通用非法字符验证 - is_forbid
 * 用途：检查是否含有非法字符（基于原始is_forbid函数）
 * 匹配的字符：* ' \ $ ^ ; < > " = { } @ , % ~ &
 */
export const GENERAL_FORBIDDEN_CHARS_REGEX = /[*'\\$^;<>"={}@,%~&]+/

/**
 * 海外非法字符验证 - is_forbid_oversea
 * 用途：检查海外地址是否含有非法字符（基于原始is_forbid_oversea函数）
 * 匹配的字符：* < > $ { } = " ^ ' \ ` ! [ ] : ? " ' @ , % ~
 */
export const OVERSEA_FORBIDDEN_CHARS_REGEX = /[*<>${}="^'\\`!\[\]:?"'@,%~]+/
/**
 * 表情符号和特殊Unicode字符验证 - is_emoji
 * 用途：检查是否含有表情符号或特殊Unicode字符（基于原始is_emoji函数）
 * 匹配：不在指定Unicode范围内的字符
 */
export const EMOJI_AND_SPECIAL_CHARS_REGEX = /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac]/
